# Elektronické schéma podle p<PERSON>hy

```mermaid
graph TD
    %% Definice stylů
    classDef logicGate fill:#f9f9f9,stroke:#333,stroke-width:2px
    classDef resistor fill:#fff,stroke:#333,stroke-width:2px
    classDef input fill:#e1f5fe,stroke:#01579b,stroke-width:2px
    
    %% Vst<PERSON><PERSON><PERSON> signály
    K1[K₁]:::input
    K2[K₂]:::input
    T[T]:::input
    
    %% Logické hradla a komponenty
    AND1["&"]:::logicGate
    AND2["&"]:::logicGate
    AND3["&"]:::logicGate
    AND4["&"]:::logicGate
    OR1["≥1"]:::logicGate
    OR2["≥1"]:::logicGate
    
    %% Výstupní odpory
    R1[R₁]:::resistor
    R2[R₂]:::resistor
    
    %% Čísla 1 v obdélnících
    NUM1_1["1"]
    NUM1_2["1"]
    
    %% Propoje<PERSON><PERSON> podle schématu
    K1 --> AND1
    K2 --> AND1
    K2 --> AND2
    T --> AND2
    T --> AND3
    
    AND1 --> OR1
    AND2 --> OR1
    AND3 --> OR2
    
    OR1 --> AND4
    NUM1_1 --> AND4
    AND4 --> R1
    
    OR2 --> NUM1_2
    NUM1_2 --> R2
    
    %% Dodatečné propojení pro věrnost schématu
    AND3 --> AND4
```

## Popis schématu

Toto schéma obsahuje:
- **Vstupní signály**: K₁, K₂, T
- **Logická hradla**: AND (&) a OR (≥1) hradla
- **Výstupní odpory**: R₁ a R₂
- **Pomocné bloky**: Čísla "1" v obdélnících

Schéma je vytvořeno podle vaší ruční předlohy a zachovává všechna propojení a komponenty.
